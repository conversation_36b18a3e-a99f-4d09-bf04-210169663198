const CONFIG = {
  // 封面大图
  COMMERCE_HOME_BANNER_ENABLE: true,

  COMMERCE_TEXT_CENTER_TITLE: 'Product Center', // 中间产品区块标题
  COMMERCE_TEXT_CENTER_DESCRIPTION:
    'The vision of NotionNext is to help you effortlessly and seamlessly build your own website, amplifying the value of your brand.', // 中间产品区块文字描述
  COMMERCE_TEXT_CENTER_CATEGORY_TITLE: 'Product Categories', // 左侧产品分类标题
  COMMERCE_TEXT_FOOTER_TITLE: 'Contact US', // COMMERCE主题页脚文案标题；按Shift+Enter键可以换行
  COMMERCE_TEXT_FOOTER_MENU_1: 'Product Center', // COMMERCE主题页脚左侧菜单标题1
  COMMERCE_TEXT_FOOTER_MENU_2: 'About US', // COMMERCE主题页脚左侧菜单标题2

  COMMERCE_FOOTER_RIGHT_IMG_URL: null, // 显示页脚右侧的图片，通常放二维码
  COMMERCE_FOOTER_RIGHT_TEXT: null, // 页脚右侧图片下的文字描述

  COMMERCE_HOME_POSTS_COUNT: 9, // 首页展示商品数
  COMMERCE_CONTACT_WHATSAPP_SHOW: true, // 是否展示whatsapp联系按钮 请配置 CONTACT_WHATSAPP
  COMMERCE_CONTACT_TELEGRAM_SHOW: true // 联系栏展示telegram按钮 请配置 CONTACT_TELEGRAM
}
export default CONFIG
